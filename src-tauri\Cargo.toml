[workspace]
members = ["common"]
default-members = ["."]

[package]
name = "sdw"
version = "1.0.44"
authors = ["SDW"]
edition = "2021"
default-run = "sdw"  # Must match the [[bin]] name

# Add binary targets section
[[bin]]
name = "sdw"  # Main application (src/main.rs)
path = "src/main.rs"


[build-dependencies]
tauri-build = { version = "1.5.2", features = [] }
tonic-build = { version = "0.12" }
prost-build = { version = "0.13" }

# 添加macOS专用依赖
[target.'cfg(target_os = "macos")'.dependencies]
objc = "0.2.7"
core-foundation = "0.9.3"
defguard_wireguard_rs = { git = "https://github.com/DefGuard/wireguard-rs.git", rev = "v0.7.0" }  # 新增DefGuard维护的wireguard-rs

[target.'cfg(target_os = "linux")'.dependencies]
defguard_wireguard_rs = { git = "https://github.com/DefGuard/wireguard-rs.git", rev = "v0.7.0" }  # 新增DefGuard维护的wireguard-rs

# 修正为（条件式引入）：
[target.'cfg(windows)'.dependencies]
winreg = "0.52.0"  # ✅ 正确：仅在 Windows 平台生效
wireguard-nt = "0.5.0"

[dependencies]
anyhow = "1.0"
tauri = { version = "1.6.6", features = ["api-all", "devtools", "system-tray", "icon-png", "icon-ico"] }
serde = { version = "1.0.202", features = ["derive"] }
common = { path = "common" }
serde_json = "1.0.117"
home = "0.5.9"
#winreg = "0.52.0"
once_cell = "1.19.0"
lazy_static = "1.4.0"
clap = { version = "4.5", features = ["cargo", "derive", "env"] }
tauri-plugin-window-state = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v1" }
tauri-plugin-single-instance = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v1" }
tauri-plugin-autostart = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v1" }
tauri-plugin-log = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v1" }
time = { version = "0.3", features = ["formatting", "macros"] }
encoding_rs="0.8.35"
regex="1.11.1"
is_elevated = "0.1.2"
winapi = { version = "0.3", features = ["iphlpapi", "ws2def", "winerror", "winuser", "wincon"] }
sysinfo = "0.33.1"
network-interface = "2.0.0"
#tokio = "1.44.1"
tokio = { version = "1", features = ["macros", "rt-multi-thread", "signal"] }
tokio-util = "0.7"
tonic = "0.12"
log = { version = "0.4", features = ["serde"] }
prost = "0.13"
thiserror = "2.0"
tracing = "0.1"
tracing-appender = "0.2"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
dirs-next = "2.0"
open = "5.3.2"
ureq = { version = "3", features= ["json"] }

futures-util  = "0.3.31"
dirs = "6.0.0"
chrono = "0.4.40"
ipnet = "2.3"
base64 = "0.13"
hickory-resolver = "0.24"
[dependencies.windows]
version = "0.43.0"
features = [
    "Win32_Foundation",
    "Win32_Security",
    "Win32_System_Threading",
    "Win32_System_JobObjects",
]

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]


[dev-dependencies]
tokio = { version = "1", features = ["full"] }
