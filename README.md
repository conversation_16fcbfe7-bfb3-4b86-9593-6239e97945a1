# Tauri + Vue + TypeScript

This template should help get you started developing with Vue 3 and TypeScript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) + [<PERSON><PERSON>](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)

## Type Support For `.vue` Imports in TS

Since TypeScript cannot handle type information for `.vue` imports, they are shimmed to be a generic Vue component type by default. In most cases this is fine if you don't really care about component prop types outside of templates. However, if you wish to get actual prop types in `.vue` imports (for example to get props validation when using manual `h(...)` calls), you can enable Volar's Take Over mode by following these steps:

1. Run `Extensions: Show Built-in Extensions` from VS Code's command palette, look for `TypeScript and JavaScript Language Features`, then right click and select `Disable (Workspace)`. By default, Take Over mode will enable itself if the default TypeScript extension is disabled.
2. Reload the VS Code window by running `Developer: Reload Window` from the command palette.

You can learn more about Take Over mode [here](https://github.com/johnsoncodehk/volar/discussions/471).


### 正式环境 
    "security": {
      "csp": "default-src 'self'; img-src 'self' https://trusted-domain.com data:; connect-src 'self' https://api-sdw.usr.cn wss://mqtt-sdw.usr.cn/mqtt; worker-src 'self' blob:;"
    }
### 提测环境 配置rust访问rc环境
    "security": {
      "csp": "default-src 'self'; img-src 'self' https://trusted-domain.com data:; connect-src 'self' https://sd.sdw.usr.86.ltd wss://mq.sd.sdw.usr.86.ltd/mqtt; worker-src 'self' blob:;"
    }

### 压力测试
    "security": {
      "csp": "default-src 'self'; img-src 'self' https://trusted-domain.com data:; connect-src 'self'  https://api.rc2.sdw.86.ltd wss://mqtt.rc2.sdw.86.ltd/mqtt; worker-src 'self' blob:;"
    },
### 管理员 build.rs
fn main() {
let mut windows = tauri_build::WindowsAttributes::new();
windows = windows.app_manifest(r#"
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
<dependency>
<dependentAssembly>
<assemblyIdentity
type="win32"
name="Microsoft.Windows.Common-Controls"
version="6.0.0.0"
processorArchitecture="*"
publicKeyToken="6595b64144ccf1df"
language="*"
/>
</dependentAssembly>
</dependency>
<trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
<security>
<requestedPrivileges>
<requestedExecutionLevel
level="requireAdministrator"
uiAccess="false"
/>
</requestedPrivileges>
</security>
</trustInfo>
</assembly>
"#);
tauri_build::try_build(
tauri_build::Attributes::new()
.windows_attributes(windows)
).expect("Failed to set admin manifest");
}

### 普通成员 build.rs
fn main() {
tauri_build::build()
}

### toml
"Win32_NetworkManagement_QoS",
"Win32_Networking_WinSock",

### mac
      "targets": ["dmg", "app"],
      "identifier": "com.sdw.macos",
### windows
      "targets": "all",
      "identifier": "com.sdw.to",
