<script lang="ts" setup>
import {onMounted, onUnmounted, ref, watch} from "vue";
import {changeNetworkStatusAPI, getAccountInfoAPI, getNetworkAndPeerAPI,} from "../../api/network.ts";
import {tunnelStore, useMqttStore, useN3nStore} from "../../store";
import {invoke} from '@tauri-apps/api/tauri';
import chart from './chart.vue'
import moment from "moment";

const mqttStore = useMqttStore()
const n3nStore: any = useN3nStore()
const tunnelStoreData: any = tunnelStore()
// 定义一个响应式的数组 mainList，用于存储从网络请求中获取的数据
const currentDevice: any = ref({})
const currentNetwork: any = ref(null)
// 获取登陆时间
const loginTime = localStorage.getItem('loginTime')
const peerTimer: any = ref(null)
const twoLevelStatus = ref(false)
// 三层组网的网卡
const trafficData: any = ref([])
const currentTrafficData: any = ref({
  up: '0bps',
  down: '0bps',
})
const cidrAddress = ref('')
onMounted(async () => {
  // 保证切换回来图表数据不为空
  trafficData.value = mqttStore.chartData
  console.log(n3nStore, tunnelStoreData, mqttStore, '进入首页')
  // 获取当前设备信息
  getAccountInfo()
  // 获取设备累计信息
  clearInterval(peerTimer.value)
  peerTimer.value = null
  getPeerData()
  peerTimer.value = setInterval(async () => {
    getPeerData()
  }, 10000)
})
/**
 * 获取当前设备，以及设备的开关状态
 */
const getAccountInfo = async () => {
  let res: any = await getAccountInfoAPI()
  if (res.msg === 'success') {
    currentDevice.value = res.result
    mqttStore.setClientName(res.result.clientName)
    setTimeout(() => {
      // 连接MQTT
      mqttStore.connect()
    }, 1000)

  }
}
/**
 * 更新当前设备
 */
const refreshAccountInfo = async () => {
  let res: any = await getAccountInfoAPI()
  if (res.msg === 'success') {
    currentDevice.value = res.result
  }
}
// 暴露方法给父组件
defineExpose({
  refreshAccountInfo
});

/**
 * 计算二层组网IP端
 */
function ipToNumber(ip: any) {
  // 将IP地址转换为32位整数
  return ip.split('.').reduce((acc: any, part: any) => (acc << 8) + parseInt(part, 10), 0);
}

function numberToIp(number: any) {
  // 将32位整数转换为IP地址
  return [
    (number >>> 24) & 0xff,
    (number >>> 16) & 0xff,
    (number >>> 8) & 0xff,
    number & 0xff,
  ].join('.');
}

function calculateNetworkAddress(gateway: any, mask: any) {
  // 计算网络地址
  const gatewayNumber = ipToNumber(gateway);
  const maskNumber = ipToNumber(mask);
  const networkNumber = gatewayNumber & maskNumber;
  return numberToIp(networkNumber);
}

function subnetMaskToCIDR(mask: any) {
  // 将子网掩码转换为CIDR前缀长度
  const maskNumber = ipToNumber(mask);
  // 正确的方法：计算二进制表示中1的个数
  return maskNumber.toString(2).replace(/0/g, '').length;
}

function gatewayToCIDR(gateway: any, mask: any) {
  // 将网关和子网掩码转换为CIDR表示法
  const networkAddress = calculateNetworkAddress(gateway, mask);
  const cidr = subnetMaskToCIDR(mask);
  return `${networkAddress}/${cidr}`;
}

/**
 * 获取组网信息，累计数据十秒一更新
 */
const getPeerData = async () => {
  let resPeer: any = await getNetworkAndPeerAPI()
  if (resPeer.msg === 'success') {
    currentNetwork.value = resPeer.result
    if (resPeer.result.wan.secondGatewayIp && resPeer.result.wan.secondNetmask) {
      const cidr = gatewayToCIDR(resPeer.result.wan.secondGatewayIp, resPeer.result.wan.secondNetmask);
      // console.log(cidr); // 输出: ***********/24
      cidrAddress.value = cidr
    }
  }
  // console.log(resPeer, 'resPeer')
}
/**
 * 格式化IP
 */
const formatIP = (ip: any) => {
  const ipv4 = ip.split('/')
  if (ipv4.length == 2) {
    return ipv4[0]
  } else {
    return ip
  }
}

/**
 * 格式化时间
 */
function secondsToDHMS(seconds: any) {
  if (!seconds) {
    return '';
  }
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分 ${remainingSeconds}秒`; // 天时分秒
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分 ${remainingSeconds}秒`; // 时分秒
  } else if (minutes > 0) {
    return `${minutes}分 ${remainingSeconds}秒`; // 分秒
  } else {
    return `${remainingSeconds}秒`; // 秒
  }
}

/**
 * 格式化十位时间戳为年月日时分秒
 */
const tsFormat = (timestamp: any) => {
  if (!timestamp) {
    return ''
  } else {
    const date = new Date(timestamp * 1000); // 10位时间戳需乘以1000
    return moment(date).format('YYYY-MM-DD HH:mm:ss')
  }
}

/**
 * 格式化累积流量
 */
function formatNetworkData(network: number): string {
  if (!network) {
    return '0KB';
  }
  if (network < 1024) {
    return `${network}B`;
  } else if (network < 1048576) {
    return `${(network / 1024).toFixed(2)}KB`;
  } else if (network < 1073741824) {
    return `${(network / (1024 * 1024)).toFixed(2)}MB`;
  } else {
    return `${(network / (1024 * 1024 * 1024)).toFixed(2)}GB`;
  }
}

/**
 * 告知后端开启关闭组网
 */
const changeNetwork = async () => {
  const flag = !currentNetwork.value.enable
  let res: any = await changeNetworkStatusAPI({
    enable: flag
  })
  let resPeer: any = await getNetworkAndPeerAPI()
  if (resPeer.msg === 'success') {
    currentNetwork.value = resPeer.result
  }
  console.log(res, flag, 'res')
  if (res.msg == 'success' && flag == false) {
    // await invoke('stop_monitor')
    // todo 清除监控
    mqttStore.setCurrentTrafficData({
      up: '0bps', down: '0bps',
    })
    mqttStore.setChartData([])
  }
}
// 销毁定时器
onUnmounted(() => {
  clearInterval(peerTimer.value)
  peerTimer.value = null
})
/**
 * 监听currentTrafficData 变化
 */
watch(
  () => mqttStore.currentTrafficData,
  async () => {
    currentTrafficData.value = mqttStore.currentTrafficData
  },
  {immediate: true}
)
watch(
  () => n3nStore.status,
  async () => {
    twoLevelStatus.value = n3nStore.status
  },
  {immediate: true}
)
</script>

<template>
  <div class="mainCard">
    <div class="card mt-24">
      <div class="card-header">
        <div class="card-line"></div>
        <div class="card-title">{{ currentDevice.name || '未命名' }}</div>
      </div>
      <div class="card-grid">
        <div class="infoItem">
          <div class="infoLabel">账户/ID:</div>
          <div class="infoContent">{{ currentDevice.clientName }}</div>
        </div>
        <div class="infoItem">
          <div class="infoLabel">账户登陆时间:</div>
          <div class="infoContent">{{ loginTime }}</div>
        </div>
        <div v-if="currentNetwork" class="infoItem">
          <div class="infoLabel">组网IP:</div>
          <div v-if="mqttStore.wgConfig.level==3" class="infoContent">{{ formatIP(currentNetwork.wgAddress) }}
            <copyBtn v-if="currentNetwork.wgAddress" :ip="currentNetwork.wgAddress"></copyBtn>
          </div>
          <div v-if="mqttStore.wgConfig.level==2" class="infoContent">{{ formatIP(currentNetwork.secondIp) }}
            <copyBtn v-if="currentNetwork.secondIp" :ip="currentNetwork.secondIp"></copyBtn>
          </div>
        </div>
      </div>
    </div>
    <template v-if="currentNetwork!=null">
      <div class="card">
        <div class="card-header">
          <div class="card-line"></div>
          <div class="card-title">{{ currentNetwork?.wan.wanName }}</div>
          <template v-if="mqttStore.wgConfig.level==2">
            <img v-if="twoLevelStatus" alt="" src="../../assets/images/online.svg">
            <img v-else alt="" src="../../assets/images/offline.svg">
          </template>
          <template v-if="mqttStore.wgConfig.level==3">
            <img v-if="tunnelStoreData.wiresockState?.tunnel_status=='CONNECTED'" alt=""
                 src="../../assets/images/online.svg">
            <img v-else alt="" src="../../assets/images/offline.svg">
          </template>
        </div>
        <div class="card-grid">
          <div class="infoItem">
            <div class="infoLabel">组网IP段:</div>
            <div v-if="mqttStore.wgConfig.level==3" class="infoContent">{{ currentNetwork?.wan.wgNet }}</div>
            <div v-if="mqttStore.wgConfig.level==2" class="infoContent">{{ cidrAddress }}</div>
          </div>
          <div class="infoItem" @click="changeNetwork">
            <div class="infoLabel" style="display: flex;align-items: center;">
              <img v-if="currentNetwork?.enable" src="../../assets/images/switchOpen.svg" alt="">
              <img v-else src="../../assets/images/switchClose.svg" alt="">
              <div class="infoContent">
                {{ currentNetwork?.enable ? '已加入网络' : '未加入网络' }}
              </div>
            </div>
          </div>
        </div>
        <div class="card-grid">
          <div class="infoItem">
            <div class="infoLabel">累计接入时长:</div>
            <div class="infoContent">{{ secondsToDHMS(currentNetwork?.totalAccessTs) || '--' }}</div>
          </div>
          <div class="infoItem">
            <div class="infoLabel">上次接入时间:</div>
            <div class="infoContent">{{ tsFormat(currentNetwork?.lastAccessAt) || '--' }}</div>
          </div>
          <div class="infoItem">
            <div class="infoLabel">本次接入用量:</div>
            <div class="infoContent">{{ formatNetworkData(currentNetwork?.curAccessTraffic) || '--' }}</div>
          </div>
          <div class="infoItem">
            <div class="infoLabel">本次接入时长:</div>
            <div class="infoContent">{{ secondsToDHMS(currentNetwork?.curAccessTs) || '--' }}</div>
          </div>
          <div class="infoItem">
            <div class="infoLabel">本次接入时间:</div>
            <div class="infoContent">{{ tsFormat(currentNetwork?.curAccessAt) || '--' }}</div>
          </div>
        </div>
      </div>
      <div class="card">
        <div class="card-header">
          <div class="card-line"></div>
          <div class="card-title">实时速率</div>
          <div style="flex: 1"></div>
          <div class="speedDownload">
            <img src="../../assets/images/download.svg" alt="">
            {{ currentTrafficData.down }}
          </div>
          <div class="speedUpload">
            <img src="../../assets/images/upload.svg" alt="">
            {{ currentTrafficData.up }}
          </div>
        </div>
        <div class="chartBox">
          <chart></chart>
        </div>
      </div>
    </template>
    <div v-else class="card">
      <div class="rightSpace">
        <img alt="" class="type" src="../../assets/images/spaceMain.svg"/>
        <div class="spaceText">未加入任何网络</div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
// 隐藏滚动条
::-webkit-scrollbar {
  display: none;
}
.mainCard{
  height: 100vh;
  overflow-y: scroll;
}
.card {
  padding: 16px 16px 0;
  background: var(--usr-banner-bg);
  margin-bottom: 8px;
  margin-right: 16px;
  border-radius: 6px;
  box-shadow: 0 2px 4px 0 var(--usr-banner-shadow);
  margin-left: 4px;

  .card-header {
    height: 24px;
    font-family: PingFangHK-Medium;
    font-weight: 500;
    font-size: 16px;
    color: var(--usr-btn-border-text);
    letter-spacing: 0;
    line-height: 24px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .card-line {
      width: 2px;
      height: 14px;
      background-image: linear-gradient(179deg, #3C78FF 0%, #0F39A8 100%);
      border-radius: 1px;
      margin-right: 8px;
    }

    .card-title {
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 16px;
      color: var(--usr-btn-border-text);
      letter-spacing: 0;
      line-height: 24px;
    }

    img {
      margin-left: 8px;
    }

    .speedUpload {
      height: 22px;
      font-family: PingFangHK-Regular;
      font-weight: 400;
      font-size: 13px;
      color: var(--usr-upload);
      letter-spacing: 0;
      line-height: 22px;
      display: flex;
      align-items: center;
    }

    .speedDownload {
      height: 22px;
      font-family: PingFangHK-Regular;
      font-weight: 400;
      font-size: 13px;
      color: var(--usr-account-change);
      letter-spacing: 0;
      line-height: 22px;
      display: flex;
      align-items: center;
    }
  }

}

.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 100%;

  .infoItem {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .infoLabel {
      height: 20px;
      font-family: PingFangHK-Regular;
      font-weight: 400;
      font-size: 13px;
      color: var(--usr-account-text);
      letter-spacing: 0;
      line-height: 20px;
      margin-right: 4px;
      img{
        width: 40px;
        height: 20px;
        margin-right: 8px;
      }
    }

    .infoContent {
      height: 20px;
      font-family: PingFangHK-Regular;
      font-weight: 400;
      font-size: 13px;
      color: var(--usr-btn-border-text);
      letter-spacing: 0;
      line-height: 20px;
    }
  }

  .mb-16 {
    margin-bottom: 16px;
  }


  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.chartBox {
  height: 328px;
}

.mt-24 {
  margin-top: 24px;
}

// space
.rightSpace {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 533px;

  .type {
    width: 180px;
    height: 180px;
  }

  .spaceText {
    width: 91px;
    height: 22px;
    font-family: PingFangHK-Regular;
    font-weight: 400;
    font-size: 13px;
    color: var(--usr-account-text);
    letter-spacing: 0;
    line-height: 22px;
    margin-top: 8px;
  }
}
</style>




